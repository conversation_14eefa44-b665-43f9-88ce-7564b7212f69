/* 关于子应用sub的基础盒子样式 */
@import url(./subStyle.scss);
body {
  margin: 0;
  padding: 0;
}
.text-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.flex-h {
  display: flex;
  align-items: center;
}
.is-flex {
  display: flex;
}

//需line-clamp-x组合一起用
.line-clamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  word-wrap: break-word;
}
// 1行
.line-clamp-1 {
  -webkit-line-clamp: 1;
}
// 2行
.line-clamp-2 {
  -webkit-line-clamp: 2;
}

// 3行
.line-clamp-3 {
  -webkit-line-clamp: 3;
}

.color-danger {
  color: #f56c6c;
}

.date-tips {
  margin-left: 4px;
  font-size: 12px;
  color: #909399;
}
.flex-1 {
  flex: 1;
}
.mt-10 {
  margin-top: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mrl-10 {
  margin: 0 10px;
}
.mtb-10 {
  margin: 10px 0;
}
.ml-5 {
  margin-left: 5px;
}
.full-height {
  height: 100%;
}
.import-icon {
  font-size: 12px;
}
.reveal-box {
  max-height: 640px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-size: 14px;
  .reveal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
  }
}
.reveal-list {
  flex: 1;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 5px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 10px rgba(85, 122, 191, 0.1);
    background: #cccccc;
  }
  .reveal-item {
    display: flex;
    align-items: center;
    padding: 10px;
  }
}

.amap-marker-label {
  border: none;
}

.hidden-scroll {
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0 !important; /* 隐藏滚动条 */
    height: 0 !important; /* 隐藏滚动条 */
  }
  /* 隐藏滚动条（WebKit） */
  &::-webkit-scrollbar {
    display: none;
  }

  /* 隐藏滚动条（Firefox） */
  @-moz-document url-prefix() {
    scrollbar-width: thin;
  }
  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  -ms-overflow-style: none;
  scrollbar-width: none; /* Firefox 64+ */
  scrollbar-color: transparent transparent;
}

/*细滚动条样式*/
.small-scroll {
  overflow-y: auto;
  overflow-y: overlay; //滚动条不占宽度，chrome下可用

  &::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: #9fa0a2;
  }

  &::-webkit-scrollbar-track {
    background: #ebebeb;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-track-piece {
    background: #ebebeb;
  }
}

.import-dropdown-button {
  margin: 0 10px;
}
